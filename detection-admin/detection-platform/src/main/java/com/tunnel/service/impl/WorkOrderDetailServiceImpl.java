package com.tunnel.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.WorkOrder;
import com.tunnel.domain.WorkOrderDetail;
import com.tunnel.mapper.WorkOrderDetailMapper;
import com.tunnel.mapper.WorkOrderMapper;
import com.tunnel.service.WorkOrderDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维护工单明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class WorkOrderDetailServiceImpl implements WorkOrderDetailService {

    @Autowired
    private WorkOrderDetailMapper workOrderDetailMapper;

    @Autowired
    private WorkOrderMapper workOrderMapper;

    /**
     * 查询维护工单明细
     *
     * @param id 维护工单明细主键
     * @return 维护工单明细
     */
    @Override
    public WorkOrderDetail selectWorkOrderDetailById(Long id) {
        return workOrderDetailMapper.selectWorkOrderDetailById(id);
    }

    /**
     * 查询维护工单明细列表
     *
     * @param workOrderDetail 维护工单明细
     * @return 维护工单明细
     */
    @Override
    public List<WorkOrderDetail> selectWorkOrderDetailList(WorkOrderDetail workOrderDetail) {
        return workOrderDetailMapper.selectWorkOrderDetailList(workOrderDetail);
    }

    /**
     * 根据工单ID查询工单明细列表
     *
     * @param orderId 工单ID
     * @return 工单明细集合
     */
    @Override
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderId(Long orderId) {
        return workOrderDetailMapper.selectWorkOrderDetailListByOrderId(orderId);
    }

    /**
     * 根据工单ID查询工单明细列表（按维护类型分组）
     *
     * @param orderId 工单ID
     * @return 按维护类型分组的工单明细Map，key为维护类型，value为明细列表
     */
    @Override
    public Map<String, List<WorkOrderDetail>> selectWorkOrderDetailListByOrderIdGrouped(Long orderId) {
        List<WorkOrderDetail> detailList = workOrderDetailMapper.selectWorkOrderDetailListByOrderIdGrouped(orderId);
        
        // 按维护类型分组
        Map<String, List<WorkOrderDetail>> groupedMap = detailList.stream()
                .collect(Collectors.groupingBy(WorkOrderDetail::getWorkType, LinkedHashMap::new, Collectors.toList()));
        
        return groupedMap;
    }

    /**
     * 根据工单ID数组查询工单明细列表
     *
     * @param orderIds 工单ID数组
     * @return 工单明细集合
     */
    @Override
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderIds(Long[] orderIds) {
        return workOrderDetailMapper.selectWorkOrderDetailListByOrderIds(orderIds);
    }

    /**
     * 新增维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWorkOrderDetail(WorkOrderDetail workOrderDetail) {
        // 校验工单ID是否存在
        if (!checkOrderIdExists(workOrderDetail.getOrderId())) {
            throw new ServiceException("工单ID不存在：" + workOrderDetail.getOrderId());
        }

        workOrderDetail.setCreateTime(DateUtils.getNowDate());
        workOrderDetail.setCreator(SecurityUtils.getUserId());
        return workOrderDetailMapper.insertWorkOrderDetail(workOrderDetail);
    }

    /**
     * 批量新增维护工单明细
     *
     * @param workOrderDetailList 工单明细列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertWorkOrderDetail(List<WorkOrderDetail> workOrderDetailList) {
        if (workOrderDetailList == null || workOrderDetailList.isEmpty()) {
            return 0;
        }

        // 校验工单ID是否存在
        Set<Long> orderIds = workOrderDetailList.stream()
                .map(WorkOrderDetail::getOrderId)
                .collect(Collectors.toSet());
        
        for (Long orderId : orderIds) {
            if (!checkOrderIdExists(orderId)) {
                throw new ServiceException("工单ID不存在：" + orderId);
            }
        }

        // 设置创建信息
        Long userId = SecurityUtils.getUserId();
        Date now = DateUtils.getNowDate();
        workOrderDetailList.forEach(detail -> {
            detail.setCreator(userId);
            detail.setCreateTime(now);
        });

        return workOrderDetailMapper.batchInsertWorkOrderDetail(workOrderDetailList);
    }

    /**
     * 修改维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWorkOrderDetail(WorkOrderDetail workOrderDetail) {
        // 校验工单ID是否存在
        if (workOrderDetail.getOrderId() != null && !checkOrderIdExists(workOrderDetail.getOrderId())) {
            throw new ServiceException("工单ID不存在：" + workOrderDetail.getOrderId());
        }

        workOrderDetail.setModifier(SecurityUtils.getUserId());
        return workOrderDetailMapper.updateWorkOrderDetail(workOrderDetail);
    }

    /**
     * 批量删除维护工单明细
     *
     * @param ids 需要删除的维护工单明细主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailByIds(Long[] ids) {
        return workOrderDetailMapper.deleteWorkOrderDetailByIds(ids);
    }

    /**
     * 删除维护工单明细信息
     *
     * @param id 维护工单明细主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailById(Long id) {
        return workOrderDetailMapper.deleteWorkOrderDetailById(id);
    }

    /**
     * 根据工单ID删除工单明细
     *
     * @param orderId 工单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailByOrderId(Long orderId) {
        return workOrderDetailMapper.deleteWorkOrderDetailByOrderId(orderId);
    }

    /**
     * 根据工单ID数组删除工单明细
     *
     * @param orderIds 工单ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailByOrderIds(Long[] orderIds) {
        return workOrderDetailMapper.deleteWorkOrderDetailByOrderIds(orderIds);
    }

    /**
     * 导出维护工单明细列表
     *
     * @param response HTTP响应
     * @param workOrderDetail 查询条件
     */
    @Override
    public void exportWorkOrderDetail(HttpServletResponse response, WorkOrderDetail workOrderDetail) {
        List<WorkOrderDetail> list = workOrderDetailMapper.selectWorkOrderDetailList(workOrderDetail);
        ExcelUtil<WorkOrderDetail> util = new ExcelUtil<WorkOrderDetail>(WorkOrderDetail.class);
        util.exportExcel(response, list, "维护工单明细数据");
    }

    /**
     * 根据工单ID统计明细数量
     *
     * @param orderId 工单ID
     * @return 明细数量
     */
    @Override
    public int countWorkOrderDetailByOrderId(Long orderId) {
        return workOrderDetailMapper.countWorkOrderDetailByOrderId(orderId);
    }

    /**
     * 根据工单ID和维护类型统计明细数量
     *
     * @param orderId 工单ID
     * @param workType 维护类型
     * @return 明细数量
     */
    @Override
    public int countWorkOrderDetailByOrderIdAndWorkType(Long orderId, String workType) {
        return workOrderDetailMapper.countWorkOrderDetailByOrderIdAndWorkType(orderId, workType);
    }

    /**
     * 获取工单的维护类型列表（去重）
     *
     * @param orderId 工单ID
     * @return 维护类型列表
     */
    @Override
    public List<String> selectWorkTypeListByOrderId(Long orderId) {
        return workOrderDetailMapper.selectWorkTypeListByOrderId(orderId);
    }

    /**
     * 检查工单ID是否存在
     *
     * @param orderId 工单ID
     * @return 结果
     */
    @Override
    public boolean checkOrderIdExists(Long orderId) {
        if (orderId == null) {
            return false;
        }
        return workOrderDetailMapper.checkOrderIdExists(orderId) > 0;
    }

    /**
     * 获取工单详情统计信息
     *
     * @param orderId 工单ID
     * @return 统计信息Map
     */
    @Override
    public Map<String, Object> getWorkOrderDetailStatistics(Long orderId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总明细数量
        int totalCount = countWorkOrderDetailByOrderId(orderId);
        statistics.put("totalCount", totalCount);
        
        // 维护类型列表
        List<String> workTypeList = selectWorkTypeListByOrderId(orderId);
        statistics.put("workTypeList", workTypeList);
        statistics.put("workTypeCount", workTypeList.size());
        
        // 按维护类型统计数量
        Map<String, Integer> workTypeCountMap = new HashMap<>();
        for (String workType : workTypeList) {
            int count = countWorkOrderDetailByOrderIdAndWorkType(orderId, workType);
            workTypeCountMap.put(workType, count);
        }
        statistics.put("workTypeCountMap", workTypeCountMap);
        
        return statistics;
    }

    /**
     * 根据工单ID获取工单详情展示数据（用于前端弹框）
     *
     * @param orderId 工单ID
     * @return 工单详情展示数据
     */
    @Override
    public Map<String, Object> getWorkOrderDetailDisplayData(Long orderId) {
        Map<String, Object> displayData = new HashMap<>();
        
        // 获取工单基本信息
        WorkOrder workOrder = workOrderMapper.selectWorkOrderById(orderId);
        if (workOrder == null) {
            throw new ServiceException("工单不存在：" + orderId);
        }
        displayData.put("workOrder", workOrder);
        
        // 获取按维护类型分组的明细数据
        Map<String, List<WorkOrderDetail>> groupedDetails = selectWorkOrderDetailListByOrderIdGrouped(orderId);
        displayData.put("groupedDetails", groupedDetails);
        
        // 获取统计信息
        Map<String, Object> statistics = getWorkOrderDetailStatistics(orderId);
        displayData.put("statistics", statistics);
        
        return displayData;
    }

    /**
     * 获取工单时间线数据
     *
     * @param orderId 工单ID
     * @return 时间线数据列表，每个元素包含timestamp、title、description、type、icon等字段
     */
    @Override
    public List<Map<String, Object>> getWorkOrderTimeline(Long orderId) {
        List<Map<String, Object>> timeline = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
        
        // 获取工单基本信息
        WorkOrder workOrder = workOrderMapper.selectWorkOrderById(orderId);
        if (workOrder == null) {
            throw new ServiceException("工单不存在：" + orderId);
        }
        
        // 获取工单明细列表，按创建时间倒序排列
        List<WorkOrderDetail> detailList = selectWorkOrderDetailListByOrderId(orderId);
        detailList.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));
        
        // 根据工单状态在最上面添加状态节点
        if (workOrder.getStatus() != null) {
            Map<String, Object> statusNode = new HashMap<>();
            
            if (workOrder.getStatus() == 1) {
                // 工单已完成 - 使用工单的更新时间
                statusNode.put("timestamp", workOrder.getUpdateTime() != null ? 
                    dateFormat.format(workOrder.getUpdateTime()) : 
                    dateFormat.format(new Date()));
                statusNode.put("title", "已完成");
                statusNode.put("description", "工单已完成");
                statusNode.put("type", "success");
                statusNode.put("icon", "el-icon-check");
            } else if (workOrder.getStatus() == 0) {
                // 工单进行中 - 使用当前时间
                statusNode.put("timestamp", dateFormat.format(new Date()));
                statusNode.put("title", "进行中");
                statusNode.put("description", "工单正在进行中");
                statusNode.put("type", "primary");
                statusNode.put("icon", "el-icon-loading");
            }
            timeline.add(statusNode);
        }
        
        // 为每个工单明细创建独立的时间线节点
        for (WorkOrderDetail detail : detailList) {
            Map<String, Object> node = new HashMap<>();
            
            // 设置时间戳
            node.put("timestamp", detail.getCreateTime() != null ? 
                dateFormat.format(detail.getCreateTime()) : 
                dateFormat.format(new Date()));
            
            // 设置节点标题和描述 - 显示具体的工作类型和工作项目
            String workType = detail.getWorkType() != null ? detail.getWorkType() : "维护工作";
            String workTitle = detail.getWorkTitle() != null ? detail.getWorkTitle() : "工作项目";
            
            node.put("title", workType);
            node.put("description", workTitle);
            
            // 根据工单状态设置节点样式
            if (workOrder.getStatus() != null && workOrder.getStatus() == 1) {
                // 工单已完成，所有明细节点显示为已完成
                node.put("type", "success");
                node.put("icon", "el-icon-check");
            } else {
                // 工单进行中，明细节点显示为进行中
                node.put("type", "primary");
                node.put("icon", "el-icon-loading");
            }
            
            // 添加额外信息
            if (detail.getRemark() != null && !detail.getRemark().trim().isEmpty()) {
                node.put("remark", detail.getRemark());
            }
            node.put("detailId", detail.getId());
            
            timeline.add(node);
        }
        
        // 最后添加工单创建节点
        Map<String, Object> createNode = new HashMap<>();
        createNode.put("timestamp", workOrder.getCreateTime() != null ? 
            dateFormat.format(workOrder.getCreateTime()) : 
            dateFormat.format(new Date()));
        createNode.put("title", "已创建");
        createNode.put("description", "工单已创建");
        createNode.put("type", "info");
        createNode.put("icon", "el-icon-plus");
        timeline.add(createNode);
        
        return timeline;
    }
}
