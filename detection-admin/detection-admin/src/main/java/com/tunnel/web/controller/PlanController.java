package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Plan;
import com.tunnel.service.PlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 服务区/收费站维护计划Controller
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/platform/plan")
@Api(tags = "服务区/收费站维护计划管理")
public class PlanController extends BaseController {
    @Autowired
    private PlanService planService;

    /**
     * 查询服务区/收费站维护计划列表
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取维护计划列表", notes = "分页查询维护计划列表")
    @Operation(summary = "获取维护计划列表", description = "分页查询维护计划列表")
    public TableDataInfo list(Plan plan) {
        startPage();
        List<Plan> list = planService.selectPlanListWithStation(plan);
        return getDataTable(list);
    }

    /**
     * 导出服务区/收费站维护计划列表
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:export')")
    @Log(title = "服务区/收费站维护计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出维护计划", notes = "导出维护计划数据到Excel")
    @Operation(summary = "导出维护计划", description = "导出维护计划数据到Excel")
    public void export(HttpServletResponse response, Plan plan) {
        planService.exportPlan(response, plan);
    }

    /**
     * 获取服务区/收费站维护计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取维护计划详细", notes = "根据ID获取维护计划详情")
    @Operation(summary = "获取维护计划详细", description = "根据ID获取维护计划详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planService.selectPlanById(id));
    }

    /**
     * 新增服务区/收费站维护计划
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:add')")
    @Log(title = "服务区/收费站维护计划", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增维护计划", notes = "新增单条维护计划")
    @Operation(summary = "新增维护计划", description = "新增单条维护计划")
    public AjaxResult add(@Validated @RequestBody Plan plan) {
        return toAjax(planService.insertPlan(plan));
    }

    /**
     * 修改服务区/收费站维护计划
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:edit')")
    @Log(title = "服务区/收费站维护计划", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改维护计划", notes = "修改维护计划")
    @Operation(summary = "修改维护计划", description = "修改维护计划")
    public AjaxResult edit(@Validated @RequestBody Plan plan) {
        return toAjax(planService.updatePlan(plan));
    }

    /**
     * 删除服务区/收费站维护计划
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:remove')")
    @Log(title = "服务区/收费站维护计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除维护计划", notes = "批量删除维护计划")
    @Operation(summary = "删除维护计划", description = "批量删除维护计划")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(planService.deletePlanByIds(ids));
    }

    /**
     * 根据站点ID查询维护计划列表
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:list')")
    @GetMapping("/listByStation/{stationId}")
    @ApiOperation(value = "根据站点查询维护计划", notes = "根据站点ID查询维护计划列表")
    @Operation(summary = "根据站点查询维护计划", description = "根据站点ID查询维护计划列表")
    @ApiImplicitParam(name = "stationId", value = "站点ID", required = true, dataType = "String", paramType = "path")
    public AjaxResult listByStation(@PathVariable String stationId) {
        List<Plan> list = planService.selectPlanListByStationId(stationId);
        return success(list);
    }

    /**
     * 校验站点ID是否存在
     */
    @GetMapping("/checkStationId/{stationId}")
    @ApiOperation(value = "校验站点ID", notes = "校验站点ID是否存在")
    @Operation(summary = "校验站点ID", description = "校验站点ID是否存在")
    @ApiImplicitParam(name = "stationId", value = "站点ID", required = true, dataType = "String", paramType = "path")
    public AjaxResult checkStationId(@PathVariable String stationId) {
        boolean exists = planService.checkStationIdExists(stationId);
        return success(exists);
    }

    /**
     * 根据站点名称查询站点ID
     */
    @GetMapping("/getStationIdByName/{stationName}")
    @ApiOperation(value = "根据站点名称查询站点ID", notes = "根据站点名称查询站点ID")
    @Operation(summary = "根据站点名称查询站点ID", description = "根据站点名称查询站点ID")
    @ApiImplicitParam(name = "stationName", value = "站点名称", required = true, dataType = "String", paramType = "path")
    public AjaxResult getStationIdByName(@PathVariable String stationName) {
        String stationId = planService.selectStationIdByName(stationName);
        return success(stationId);
    }

    /**
     * 导入维护计划数据
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:import')")
    @Log(title = "服务区/收费站维护计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation(value = "导入维护计划", notes = "从Excel文件导入维护计划数据")
    @Operation(summary = "导入维护计划", description = "从Excel文件导入维护计划数据")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        String message = planService.importPlan(file, updateSupport);
        return success(message);
    }

    /**
     * 导入维护计划数据（使用简化模板）
     */
    @PreAuthorize("@ss.hasPermi('platform:plan:import')")
    @Log(title = "服务区/收费站维护计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importDataByVO")
    @ApiOperation(value = "导入维护计划（简化模板）", notes = "从Excel文件导入维护计划数据（维护时间、服务区、备注三列）")
    @Operation(summary = "导入维护计划（简化模板）", description = "从Excel文件导入维护计划数据（维护时间、服务区、备注三列）")
    public AjaxResult importDataByVO(MultipartFile file, boolean updateSupport) throws Exception {
        String message = planService.importPlanByVO(file, updateSupport);
        return success(message);
    }

    /**
     * 下载维护计划导入模板
     */
    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载维护计划导入模板")
    @Operation(summary = "下载导入模板", description = "下载维护计划导入模板")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<Plan> util = new ExcelUtil<Plan>(Plan.class);
        util.importTemplateExcel(response, "维护计划数据");
    }

    /**
     * 下载维护计划简化导入模板
     */
    @PostMapping("/importTemplateSimple")
    @ApiOperation(value = "下载简化导入模板", notes = "下载维护计划简化导入模板（维护时间、服务区、备注三列）")
    @Operation(summary = "下载简化导入模板", description = "下载维护计划简化导入模板（维护时间、服务区、备注三列）")
    public void importTemplateSimple(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("维护计划简化导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            
            // 获取模板文件输入流
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("public/excel/import_plan_template.xlsx");
            if (inputStream == null) {
                throw new RuntimeException("模板文件不存在：public/excel/import_plan_template.xlsx");
            }
            
            // 将文件内容写入响应输出流
            OutputStream outputStream = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            
            // 关闭流
            inputStream.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            throw new RuntimeException("下载模板文件失败：" + e.getMessage(), e);
        }
    }
}
