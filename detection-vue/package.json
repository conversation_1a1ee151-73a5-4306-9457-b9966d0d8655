{"name": "ruoyi", "version": "3.8.4", "description": "环保数字化平台", "author": "若依", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@jiaminghi/data-view": "^2.10.0", "@vueuse/core": "9.5.0", "autofit.js": "^3.2.8", "axios": "0.27.2", "d3-geo": "^3.1.1", "delaunator": "^5.0.1", "echarts": "^5.4.3", "element-plus": "2.2.21", "file-saver": "2.0.5", "fuse.js": "6.6.2", "gsap": "^3.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "loadsh": "^0.0.4", "normalize.css": "^8.0.1", "nprogress": "0.2.0", "pinia": "2.0.22", "point-in-polygon": "^1.1.0", "sortablejs": "^1.15.6", "three": "^0.161.0", "three.interactive": "^1.8.0", "tiny-emitter": "^2.1.0", "uuid": "^9.0.1", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-echarts": "^6.7.3", "vue-router": "4.1.4"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "axios": "0.27.2", "sass": "1.56.1", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}