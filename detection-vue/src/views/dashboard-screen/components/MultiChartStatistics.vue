<template>
  <div class="multi-chart-statistics">
    <div class="chart-header">
      <div class="title">安陆服务区</div>
      <div class="tabs">
        <div class="tab active">设备监控</div>
        <div class="tab">指标监测</div>
      </div>
    </div>
    
    <div class="charts-container">
      <!-- 左侧图表组 -->
      <div class="left-charts">
        <div class="chart-item">
          <div class="chart-title">上报数据</div>
          <div class="chart-placeholder line-chart" :style="{ '--color': '#00FFFF' }"></div>
        </div>
        <div class="chart-item">
          <div class="chart-title">状态数据</div>
          <div class="chart-placeholder line-chart" :style="{ '--color': '#FF4081' }"></div>
        </div>
        <div class="chart-item">
          <div class="chart-title">报警数据</div>
          <div class="chart-placeholder line-chart" :style="{ '--color': '#FFDD44' }"></div>
        </div>
      </div>
      
      <!-- 右侧图表组 -->
      <div class="right-charts">
        <div class="chart-item">
          <div class="chart-title">水压/流量统计</div>
          <div class="chart-placeholder line-chart" :style="{ '--color': '#00E5CC' }"></div>
        </div>
        <div class="chart-item">
          <div class="chart-title">区域统计</div>
          <div class="chart-placeholder line-chart" :style="{ '--color': '#66CCFF' }"></div>
        </div>
        <div class="chart-item pie-chart-container">
          <div class="chart-title">数据状态</div>
          <div class="chart-placeholder pie-chart">
            <div class="pie-segment green"></div>
            <div class="pie-segment yellow"></div>
            <div class="pie-segment red"></div>
            <div class="pie-center">
              <div class="pie-value">77%</div>
            </div>
          </div>
          <div class="pie-legend">
            <div class="legend-item">
              <div class="legend-color green"></div>
              <span>数据正常</span>
            </div>
            <div class="legend-item">
              <div class="legend-color yellow"></div>
              <span>数据异常</span>
            </div>
            <div class="legend-item">
              <div class="legend-color red"></div>
              <span>无数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
</script>

<style lang="scss">
.multi-chart-statistics {
  width: 100%;
  height: 100%;
  background: rgba(0, 29, 77, 0.8);
  border: 1px solid rgba(0, 191, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid rgba(0, 191, 255, 0.2);
    
    .title {
      color: #FFFFFF;
      font-size: 16px;
      font-weight: 700;
    }
    
    .tabs {
      display: flex;
      gap: 20px;
      
      .tab {
        color: #FFFFFF;
        font-size: 14px;
        opacity: 0.6;
        cursor: pointer;
        transition: opacity 0.3s;
        
        &.active {
          opacity: 1;
          color: #00BFFF;
          font-weight: 700;
        }
      }
    }
  }
  
  .charts-container {
    flex: 1;
    display: flex;
    gap: 15px;
    padding: 15px;
    
    .left-charts,
    .right-charts {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    
    .chart-item {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      padding: 10px;
      position: relative;
      
      .chart-title {
        color: #FFFFFF;
        font-size: 12px;
        margin-bottom: 8px;
      }
      
      .chart-placeholder {
        width: 100%;
        height: 60px;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        position: relative;
        overflow: hidden;
        
        &.line-chart::after {
          content: '';
          position: absolute;
          bottom: 20px;
          left: 0;
          right: 0;
          height: 2px;
          background: var(--color);
          border-radius: 1px;
        }
      }
    }
    
    .pie-chart-container {
      display: flex;
      flex-direction: column;
      
      .pie-chart {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.2);
        margin: 0 auto;
        position: relative;
        overflow: hidden;
        
        .pie-segment {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          transform-origin: center;
        }
        
        .green {
          background: #00E5CC;
          clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%);
          transform: rotate(0deg);
        }
        
        .yellow {
          background: #FFDD44;
          clip-path: polygon(50% 50%, 0% 0%, 50% 0%);
          transform: rotate(135deg);
        }
        
        .red {
          background: #FF4081;
          clip-path: polygon(50% 50%, 0% 0%, 0% 100%, 50% 100%);
          transform: rotate(45deg);
        }
        
        .pie-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 60%;
          height: 60%;
          border-radius: 50%;
          background: rgba(0, 29, 77, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid rgba(0, 236, 255, 0.3);
          
          .pie-value {
            color: #00E5CC;
            font-size: 14px;
            font-weight: 700;
          }
        }
      }
      
      .pie-legend {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-top: 10px;
        
        .legend-item {
          display: flex;
          align-items: center;
          font-size: 10px;
          color: #FFFFFF;
          
          .legend-color {
            width: 8px;
            height: 8px;
            border-radius: 2px;
            margin-right: 4px;
          }
          
          .green { background: #00E5CC; }
          .yellow { background: #FFDD44; }
          .red { background: #FF4081; }
        }
      }
    }
  }
}
</style>