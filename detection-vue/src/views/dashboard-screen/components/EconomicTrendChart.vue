<template>
  <div class="left-card">
    <m-card title="环境质量指数">
      <m-line-3d
        ref="line3d"
        :data="chartData"
        :categories="categories"
        :colors="['#6BC7F6']"
        :delay="4000"
      />
    </m-card>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from "vue"
import mCard from "@/components/dashboard/mCard/index.vue"
import mLine3d from "@/components/dashboard/mLine3d/index.vue"

const line3d = ref(null)

const chartData = ref([
  [85, 92, 78, 88, 95, 89, 82, 90, 87, 93, 86, 91]
])

const categories = ref([
  "04月",
  "05月", 
  "06月",
  "07月",
  "08月",
  "09月",
  "10月",
  "11月",
  "12月",
  "01月",
  "02月",
  "03月",
])

</script>
<style lang="scss"></style>
