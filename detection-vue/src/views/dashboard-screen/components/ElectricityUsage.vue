<template>
  <div class="right-card">
    <m-card title="污染物排放监测">
      <m-line-3d
        ref="line3d"
        :data="chartData"
        :categories="categories"
        :colors="['#44E6A2', '#6BC7F6']"
        :delay="4500"
      />
    </m-card>
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue"
import mCard from "@/components/mCard/index.vue"
import mLine3d from "@/components/mLine3d/index.vue"

const line3d = ref(null)

const chartData = ref([
  [45, 38, 52, 41, 35, 48, 55, 42, 39, 46, 50, 43], // COD排放量
  [28, 35, 31, 42, 38, 33, 29, 36, 44, 32, 40, 37]  // 氨氮排放量
])

const categories = ref([
  "04月",
  "05月",
  "06月",
  "07月",
  "08月",
  "09月",
  "10月",
  "11月",
  "12月",
  "01月",
  "02月",
  "03月",
])
</script>

<style lang="scss"></style>
