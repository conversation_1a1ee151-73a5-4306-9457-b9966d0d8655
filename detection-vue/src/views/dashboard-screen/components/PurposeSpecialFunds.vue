<template>
  <div class="right-card">
    <m-card title="设备总览">
      <m-circle-progress />
    </m-card>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from "vue"
import mCard from "@/components/dashboard/mCard/index.vue"
import mCircleProgress from "@/components/dashboard/mCircleProgress/index.vue"

// 组件逻辑已移至 mCircleProgress 组件内部

</script>

<style lang="scss">
// 样式已移至 mCircleProgress 组件内部
</style>
