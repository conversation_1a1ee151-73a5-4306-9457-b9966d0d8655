<template>
  <div class="left-card">
    <m-card title="污染源监测数据">
      <div class="bar-chart-wrap">
        <div class="bar-chart">
          <m-bar-3d
            ref="barChart"
            :data="state.barData"
            :colors="state.barColors"
            class="barCanvas"
          />
        </div>
        <div class="bar-legend">
          <div class="bar-legend-item" v-for="(item, index) in state.barData" :key="index">
            <div class="icon" :style="{ backgroundColor: state.barColors[index] }"></div>
            <div class="name">{{ item.name }}</div>
            <div class="value">{{ item.value }}<span class="unit">个</span></div>
          </div>
        </div>
      </div>
    </m-card>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, nextTick, onBeforeUnmount } from "vue"
import mCard from "@/components/mCard/index.vue"
import mBar3d from "@/components/mBar3d/index.vue"

const barChart = ref(null)
const state = reactive({
  barColors: ["#00FFFF", "#00FF88", "#66CCFF", "#FFDD44", "#FF8888"],
  barData: [
    {
      name: "工业废水",
      value: 85,
    },
    {
      name: "生活污水",
      value: 120,
    },
    {
      name: "大气污染",
      value: 95,
    },
    {
      name: "噪声污染",
      value: 65,
    },
    {
      name: "固废污染",
      value: 75,
    },
  ],
})
</script>
<style lang="scss">
.bar-chart-wrap {
  width: 100%;
  height: 100%;
  display: flex;
}

// 3D柱状图
.bar-chart {
  pointer-events: all;
  position: relative;
  width: 60%;
  height: 100%;

  .barCanvas {
    width: 100%;
    height: 100%;
    pointer-events: all;
  }
}

// 柱状图图例
.bar-legend {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  padding: 20px 0;
  width: 40%;

  &-item {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    box-sizing: border-box;
    margin-bottom: 8px;

    .icon {
      width: 8px;
      height: 8px;
      border-radius: 2px;
      margin-right: 8px;
    }

    .name {
      font-weight: 500;
      font-size: 10px;
      color: #ffffff;
      width: 40px;
    }

    .value {
      display: flex;
      flex-wrap: nowrap;
      align-items: flex-end;
      justify-content: flex-end;
      width: 60px;
      text-align: right;
      font-weight: bold;
      color: #ffffff;
      font-family: D-DIN;
      font-size: 14px;

      .unit {
        font-family: D-DIN;
        font-weight: 400;
        font-size: 8px;
        color: #ffffff;
        opacity: 0.5;
        padding-left: 4px;
      }
    }
  }
}
</style>
