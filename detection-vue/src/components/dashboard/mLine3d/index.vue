<template>
  <div class="three-line-wrap">
    <div class="three-line" ref="lineDom"></div>
  </div>
</template>

<script>
import * as THREE from "three"
import gsap from "gsap"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
import { emptyObject } from "@/utils/index"

import ring2 from "@/assets/texture/pie/ring2.png"
import ring3 from "@/assets/texture/pie/ring3.png"
import ring4 from "@/assets/texture/pie/ring4.png"

export default {
  name: "ThreeLine",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    categories: {
      type: Array,
      default: () => [],
    },
    colors: {
      type: Array,
      default: () => ["#6BC7F6", "#44E6A2"],
    },
    opacity: {
      type: Number,
      default: 0.8,
    },
    delay: {
      type: Number,
      default: 3000,
    },
  },
  data() {
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.axes = null
    this.lineGroup = new THREE.Group()
    this.gridGroup = new THREE.Group()
    this.particles = null
    return {
      width: 300,
      height: 200,
      activeIndex: 0,
      timer: null,
      maxValue: 0,
    }
  },
  mounted() {
    this.width = this.$refs["lineDom"].offsetWidth
    this.height = this.$refs["lineDom"].offsetHeight
    this.maxValue = Math.max(...this.data.flat())
    this.init()
  },
  beforeUnmount() {
    clearInterval(this.timer)
    this.destroy()
  },
  methods: {
    init() {
      this.scene = new THREE.Scene()
      this.scene.background = null
      this.initCamera()
      this.initRenderer()
      this.initLight()
      this.initAxes()
      this.initControls()
      this.createGrid()
      this.createLines()
      this.createParticles()
      this.createBackground()
      this.loop()
    },

    createGrid() {
      // 创建3D纹理底座
      this.createPlane({
        url: ring2,
        width: 12,
        position: new THREE.Vector3(0, -0.01, 0),
        color: "#00ffff",
      })
      this.createPlane({
        url: ring3,
        width: 14,
        position: new THREE.Vector3(0, -0.02, 0),
        color: "#00ffff",
      })
      this.createPlane({
        url: ring4,
        width: 13,
        position: new THREE.Vector3(0, -0.03, 0),
        animate: true,
        color: "#00ffff",
      })

      this.scene.add(this.gridGroup)
    },

    createPlane(opt) {
      let defaultOpt = {
        url: "texture/ring1.png",
        width: 5.5,
        z: 0,
        position: new THREE.Vector3(0, 0, 0),
        animate: false,
        color: null,
      }
      let options = Object.assign(defaultOpt, opt)
      const geometry = new THREE.PlaneGeometry(options.width, options.width)
      const material = new THREE.MeshBasicMaterial({
        map: this.getTexture(options.url),
        transparent: true,
        side: THREE.DoubleSide,
        depthTest: false,
      })
      if (options.color) {
        material.color = new THREE.Color(options.color)
      }
      const mesh = new THREE.Mesh(geometry, material)
      mesh.position.copy(options.position)
      mesh.rotation.x = (-1 * Math.PI) / 2
      if (options.animate) {
        gsap.to(mesh.rotation, {
          z: 2 * Math.PI,
          repeat: -1,
          ease: "none",
          duration: 3,
        })
      }
      this.gridGroup.add(mesh)
    },

    getTexture(url) {
      const texture = new THREE.TextureLoader().load(url)
      texture.wrapS = texture.wrapT = THREE.RepeatWrapping
      return texture
    },

    createParticles() {
      // 创建浮动粒子效果
      const particleCount = 80
      const positions = new Float32Array(particleCount * 3)
      const colors = new Float32Array(particleCount * 3)

      for (let i = 0; i < particleCount; i++) {
        positions[i * 3] = (Math.random() - 0.5) * 25
        positions[i * 3 + 1] = Math.random() * 8
        positions[i * 3 + 2] = (Math.random() - 0.5) * 25

        const color = new THREE.Color()
        color.setHSL(Math.random() * 0.2 + 0.5, 0.7, 0.5)
        colors[i * 3] = color.r
        colors[i * 3 + 1] = color.g
        colors[i * 3 + 2] = color.b
      }

      const particleGeometry = new THREE.BufferGeometry()
      particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
      particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))

      const particleMaterial = new THREE.PointsMaterial({
        size: 0.05,
        vertexColors: true,
        transparent: true,
        opacity: 0.3,
        blending: THREE.AdditiveBlending
      })

      this.particles = new THREE.Points(particleGeometry, particleMaterial)
      this.scene.add(this.particles)
    },

    createBackground() {
      // 创建星空背景
      const starGeometry = new THREE.BufferGeometry()
      const starCount = 60
      const starPositions = new Float32Array(starCount * 3)

      for (let i = 0; i < starCount; i++) {
        const radius = 35
        const theta = Math.random() * Math.PI * 2
        const phi = Math.random() * Math.PI

        starPositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta)
        starPositions[i * 3 + 1] = radius * Math.cos(phi)
        starPositions[i * 3 + 2] = radius * Math.sin(phi) * Math.sin(theta)
      }

      starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3))

      const starMaterial = new THREE.PointsMaterial({
        color: 0x00ffff,
        size: 0.3,
        transparent: true,
        opacity: 0.4
      })

      const stars = new THREE.Points(starGeometry, starMaterial)
      this.scene.add(stars)
    },

    createLines() {
      const lineSpacing = 2
      const pointSpacing = 10 / (this.categories.length - 1)
      const startX = -5

      this.data.forEach((lineData, lineIndex) => {
        this.createSingleLine(lineData, lineIndex, startX, pointSpacing, lineSpacing)
      })

      this.scene.add(this.lineGroup)
      this.animateLines()
    },

    createSingleLine(lineData, lineIndex, startX, pointSpacing, lineSpacing) {
      const color = new THREE.Color(this.colors[lineIndex % this.colors.length])
      const points = []
      
      // 创建线条路径点
      lineData.forEach((value, index) => {
        const x = startX + index * pointSpacing
        const y = (value / this.maxValue) * 4 - 1
        const z = lineIndex * lineSpacing - lineSpacing / 2
        points.push(new THREE.Vector3(x, y, z))
      })

      // 创建3D线条
      const lineGeometry = new THREE.BufferGeometry().setFromPoints(points)
      const lineMaterial = new THREE.LineBasicMaterial({
        color: color,
        linewidth: 3,
        transparent: true,
        opacity: 0.9
      })
      const line = new THREE.Line(lineGeometry, lineMaterial)
      line.name = `line${lineIndex}`
      this.lineGroup.add(line)

      // 创建山峰填充效果
      this.createMountainFill(points, color, lineIndex)

      // 创建数据点
      points.forEach((point, pointIndex) => {
        this.createDataPoint(point, color, lineIndex, pointIndex, lineData[pointIndex])
      })

      // 创建类别标签
      if (lineIndex === 0) {
        points.forEach((point, pointIndex) => {
          if (this.categories[pointIndex]) {
            this.createCategoryLabel(this.categories[pointIndex], new THREE.Vector3(point.x, -1.5, 0))
          }
        })
      }
    },

    createMountainFill(points, color, lineIndex) {
      // 创建积木版山峰效果
      const blockGroup = new THREE.Group()
      blockGroup.name = `fill${lineIndex}`
      
      const blockWidth = 0.3
      const blockDepth = 0.3
      const baseY = -1
      
      points.forEach((point, index) => {
        const height = point.y - baseY
        if (height > 0) {
          // 计算需要堆叠的积木块数量
          const blockCount = Math.max(1, Math.ceil(height / 0.2))
          const actualBlockHeight = height / blockCount
          
          for (let i = 0; i < blockCount; i++) {
            // 创建单个积木块
            const blockGeometry = new THREE.BoxGeometry(blockWidth, actualBlockHeight, blockDepth)
            
            // 为每个积木块创建稍微不同的颜色，增加层次感
            const blockColor = color.clone()
            const brightness = 0.8 + (i / blockCount) * 0.4 // 越高越亮
            blockColor.multiplyScalar(brightness)
            
            const blockMaterial = new THREE.MeshLambertMaterial({
              color: blockColor,
              transparent: true,
              opacity: 0.85,
              // 添加边缘高光效果
              emissive: blockColor.clone().multiplyScalar(0.1)
            })
            
            const block = new THREE.Mesh(blockGeometry, blockMaterial)
            
            // 设置积木块位置
            const blockY = baseY + (i + 0.5) * actualBlockHeight
            block.position.set(point.x, blockY, point.z)
            
            // 添加轻微的随机旋转，增加积木感
            block.rotation.y = (Math.random() - 0.5) * 0.1
            
            // 添加边框线条效果
            const edges = new THREE.EdgesGeometry(blockGeometry)
            const lineMaterial = new THREE.LineBasicMaterial({ 
              color: 0xffffff, 
              transparent: true, 
              opacity: 0.3 
            })
            const wireframe = new THREE.LineSegments(edges, lineMaterial)
            wireframe.position.copy(block.position)
            wireframe.rotation.copy(block.rotation)
            
            blockGroup.add(block)
            blockGroup.add(wireframe)
          }
        }
      })
      
      this.lineGroup.add(blockGroup)
    },

    createDataPoint(position, color, lineIndex, pointIndex, value) {
      // 创建积木风格的数据点 - 使用立方体
      const pointGeometry = new THREE.BoxGeometry(0.15, 0.15, 0.15)
      const pointMaterial = new THREE.MeshPhongMaterial({
        color: color.clone().multiplyScalar(1.2), // 稍微亮一些
        transparent: true,
        opacity: 1.0,
        emissive: color.clone().multiplyScalar(0.4),
        shininess: 100
      })

      const point = new THREE.Mesh(pointGeometry, pointMaterial)
      // 将数据点放在积木堆的顶部
      point.position.set(position.x, position.y + 0.1, position.z)
      point.name = `point${lineIndex}_${pointIndex}`
      point.userData = { value: value, lineIndex: lineIndex, pointIndex: pointIndex }

      // 添加轻微旋转动画
      point.rotation.set(
        (Math.random() - 0.5) * 0.2,
        (Math.random() - 0.5) * 0.2,
        (Math.random() - 0.5) * 0.2
      )

      // 添加边框线条
      const edges = new THREE.EdgesGeometry(pointGeometry)
      const edgeMaterial = new THREE.LineBasicMaterial({ 
        color: 0xffffff, 
        transparent: true, 
        opacity: 0.8 
      })
      const wireframe = new THREE.LineSegments(edges, edgeMaterial)
      wireframe.position.copy(point.position)
      wireframe.rotation.copy(point.rotation)

      // 添加发光效果 - 改为立方体发光
      const glowGeometry = new THREE.BoxGeometry(0.2, 0.2, 0.2)
      const glowMaterial = new THREE.MeshBasicMaterial({
        color: color,
        transparent: true,
        opacity: 0.2,
        side: THREE.BackSide
      })
      const glow = new THREE.Mesh(glowGeometry, glowMaterial)
      glow.position.copy(point.position)
      glow.rotation.copy(point.rotation)

      this.lineGroup.add(glow)
      this.lineGroup.add(wireframe)
      this.lineGroup.add(point)

      // 创建数值标签，位置稍微调整
      this.createValueLabel(value, point.position.clone().add(new THREE.Vector3(0, 0.25, 0)))
    },

    createValueLabel(value, position) {
      // 创建文字纹理
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 128
      canvas.height = 64

      context.shadowColor = '#00ffff'
      context.shadowBlur = 10
      context.fillStyle = '#ffffff'
      context.font = 'bold 18px Arial'
      context.textAlign = 'center'
      context.strokeStyle = '#00ffff'
      context.lineWidth = 1
      context.strokeText(value.toString(), 64, 35)
      context.fillText(value.toString(), 64, 35)

      const texture = new THREE.CanvasTexture(canvas)
      const material = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        alphaTest: 0.1
      })
      const sprite = new THREE.Sprite(material)
      sprite.position.copy(position)
      sprite.scale.set(0.6, 0.3, 1)

      this.lineGroup.add(sprite)
    },

    createCategoryLabel(text, position) {
      // 创建类别标签
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 128
      canvas.height = 32

      context.fillStyle = '#ffffff'
      context.font = 'bold 14px Arial'
      context.textAlign = 'center'
      context.shadowColor = '#00ffff'
      context.shadowBlur = 6
      context.fillText(text, 64, 20)

      const texture = new THREE.CanvasTexture(canvas)
      const material = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        alphaTest: 0.1
      })
      const sprite = new THREE.Sprite(material)
      sprite.position.copy(position)
      sprite.scale.set(1.0, 0.25, 1)

      this.lineGroup.add(sprite)
    },

    animateLines() {
      // 线条动画
      this.lineGroup.children.forEach((child, index) => {
        if (child.name.startsWith('line')) {
          child.material.opacity = 0
          gsap.to(child.material, {
            opacity: 0.9,
            duration: 1.5,
            delay: index * 0.1,
            ease: "power2.out"
          })
        }
        
        // 积木块组动画
        if (child.name.startsWith('fill') && child.type === 'Group') {
          // 遍历积木块组中的所有积木块
          child.children.forEach((block, blockIndex) => {
            if (block.type === 'Mesh' && block.geometry.type === 'BoxGeometry') {
              // 初始化积木块为缩放为0
              block.scale.set(0, 0, 0)
              block.material.opacity = 0
              
              // 积木块逐个从底部向上出现的动画
              gsap.to(block.scale, {
                x: 1, y: 1, z: 1,
                duration: 0.6,
                delay: index * 0.2 + blockIndex * 0.05,
                ease: "bounce.out"
              })
              
              gsap.to(block.material, {
                opacity: 0.85,
                duration: 0.4,
                delay: index * 0.2 + blockIndex * 0.05,
                ease: "power2.out"
              })
            }
            
            // 边框线条动画
            if (block.type === 'LineSegments') {
              block.material.opacity = 0
              gsap.to(block.material, {
                opacity: 0.3,
                duration: 0.4,
                delay: index * 0.2 + blockIndex * 0.05 + 0.1,
                ease: "power2.out"
              })
            }
          })
        }
        
        // 数据点动画
        if (child.name.startsWith('point')) {
          child.scale.set(0, 0, 0)
          gsap.to(child.scale, {
            x: 1, y: 1, z: 1,
            duration: 0.8,
            delay: index * 0.1 + 0.5,
            ease: "back.out(1.7)"
          })
        }
      })

      // 开始循环高亮动画
      setTimeout(() => {
        this.timer = setInterval(() => {
          this.highlightLine()
        }, this.delay)
      }, 3000) // 延长等待时间，让积木块动画完成
    },

    highlightLine() {
      // 重置所有线条和积木块
      this.lineGroup.children.forEach(child => {
        if (child.name.startsWith('line')) {
          gsap.to(child.material, { opacity: 0.6, duration: 0.5 })
        }
        if (child.name.startsWith('fill') && child.type === 'Group') {
          // 重置积木块组中的所有积木块
          child.children.forEach(block => {
            if (block.type === 'Mesh' && block.geometry.type === 'BoxGeometry') {
              gsap.to(block.material, { opacity: 0.4, duration: 0.5 })
            }
          })
        }
      })

      // 高亮当前线条和积木块
      const currentLine = this.lineGroup.getObjectByName(`line${this.activeIndex}`)
      const currentFill = this.lineGroup.getObjectByName(`fill${this.activeIndex}`)

      if (currentLine) {
        gsap.to(currentLine.material, { opacity: 1.0, duration: 0.5 })
      }
      if (currentFill && currentFill.type === 'Group') {
        // 高亮当前积木块组
        currentFill.children.forEach((block, blockIndex) => {
          if (block.type === 'Mesh' && block.geometry.type === 'BoxGeometry') {
            gsap.to(block.material, { 
              opacity: 0.9, 
              duration: 0.5,
              delay: blockIndex * 0.02 // 积木块逐个高亮
            })
          }
        })
      }

      this.activeIndex = (this.activeIndex + 1) % this.data.length
    },

    initCamera() {
      const rate = this.width / this.height
      this.camera = new THREE.PerspectiveCamera(45, rate, 0.1, 1000)
      this.camera.position.set(8, 5, 8)
      this.camera.lookAt(0, 0, 0)
    },

    initRenderer() {
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        premultipliedAlpha: false,
      })
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.setSize(this.width, this.height)
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      this.renderer.setClearColor(0x000000, 0)
      this.$refs["lineDom"].appendChild(this.renderer.domElement)
    },

    initLight() {
      // 主光源
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5)
      directionalLight.position.set(10, 10, 5)
      directionalLight.castShadow = true

      // 环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 1.0)

      // 彩色点光源
      const pointLight1 = new THREE.PointLight(0x00ffff, 1.2, 20)
      pointLight1.position.set(-5, 8, 5)

      const pointLight2 = new THREE.PointLight(0x17e6c3, 1.0, 15)
      pointLight2.position.set(5, 6, -3)

      this.scene.add(directionalLight)
      this.scene.add(ambientLight)
      this.scene.add(pointLight1)
      this.scene.add(pointLight2)
    },

    initAxes() {
      this.axes = new THREE.AxesHelper(0)
      this.scene.add(this.axes)
    },

    initControls() {
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      this.controls.enableZoom = true
      this.controls.enablePan = false
      this.controls.maxPolarAngle = Math.PI / 2.2
      this.controls.minPolarAngle = Math.PI / 6
      this.controls.minDistance = 6
      this.controls.maxDistance = 25
      this.controls.target.set(0, 0, 0)
      this.controls.autoRotate = true
      this.controls.autoRotateSpeed = 0.3
    },

    loop() {
      this.controls.update()

      // 粒子动画
      if (this.particles) {
        const positions = this.particles.geometry.attributes.position.array
        for (let i = 0; i < positions.length; i += 3) {
          positions[i + 1] += 0.008
          if (positions[i + 1] > 8) {
            positions[i + 1] = 0
          }
        }
        this.particles.geometry.attributes.position.needsUpdate = true
        this.particles.rotation.y += 0.001
      }

      this.renderer.render(this.scene, this.camera)
      requestAnimationFrame(() => this.loop())
    },

    resize() {
      this.width = this.$refs["lineDom"].offsetWidth
      this.height = this.$refs["lineDom"].offsetHeight
      this.camera.aspect = this.width / this.height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(this.width, this.height)
    },

    destroy() {
      clearInterval(this.timer)
      if (this.renderer) {
        emptyObject(this.lineGroup)
        emptyObject(this.gridGroup)
        if (this.particles) {
          this.scene.remove(this.particles)
          this.particles.geometry.dispose()
          this.particles.material.dispose()
        }
        this.renderer.dispose()
        this.renderer.forceContextLoss()
        this.controls.dispose()
        this.$refs["lineDom"].innerHTML = ""
        this.scene = null
        this.camera = null
        this.renderer = null
        this.controls = null
        this.axes = null
        this.particles = null
      }
    },
  },
}
</script>

<style>
.three-line-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.three-line {
  width: 100%;
  height: 100%;
}
</style>
