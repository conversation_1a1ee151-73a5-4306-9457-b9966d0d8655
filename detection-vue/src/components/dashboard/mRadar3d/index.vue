<template>
  <div class="three-radar-wrap">
    <div class="three-radar" ref="radarDom"></div>
  </div>
</template>

<script>
import * as THREE from "three"
import gsap from "gsap"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls"
import { emptyObject } from "@/mini3d"

export default {
  name: "ThreeRadar",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    indicators: {
      type: Array,
      default: () => [],
    },
    colors: {
      type: Array,
      default: () => ["#FF0080", "#00FF80", "#0080FF", "#FF8000", "#8000FF"],
    },
    opacity: {
      type: Number,
      default: 0.85,
    },
    delay: {
      type: Number,
      default: 3000,
    },
  },
  data() {
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.radarGroup = new THREE.Group()
    this.gridGroup = new THREE.Group()
    return {
      width: 300,
      height: 200,
      activeIndex: 0,
      timer: null,
      maxValue: 100,
      radarRadius: 3,
      towerHeight: 2.5, // 宝塔高度
    }
  },
  mounted() {
    this.width = this.$refs["radarDom"].offsetWidth
    this.height = this.$refs["radarDom"].offsetHeight
    this.init()
  },
  beforeUnmount() {
    clearInterval(this.timer)
    this.destroy()
  },
  methods: {
    init() {
      this.scene = new THREE.Scene()
      this.scene.background = null
      this.initCamera()
      this.initRenderer()
      this.initLight()
      this.initControls()
      this.createRadarGrid()
      this.createRadarData()
      this.createLabels()
      this.loop()
    },

    createRadarGrid() {
      const sides = this.indicators.length
      const levels = 5

      // 创建宝塔形雷达网格
      for (let level = 1; level <= levels; level++) {
        const radius = (this.radarRadius * level) / levels
        // 宝塔形状：越往上越小，高度递增
        const height = (this.towerHeight * level) / levels
        const topRadius = radius * (1 - level * 0.15) // 顶部收缩

        const points = []
        const topPoints = []

        // 底部圆环
        for (let i = 0; i <= sides; i++) {
          const angle = (i * 2 * Math.PI) / sides - Math.PI / 2
          const x = Math.cos(angle) * radius
          const z = Math.sin(angle) * radius
          points.push(new THREE.Vector3(x, 0, z))
        }

        // 顶部圆环
        for (let i = 0; i <= sides; i++) {
          const angle = (i * 2 * Math.PI) / sides - Math.PI / 2
          const x = Math.cos(angle) * topRadius
          const z = Math.sin(angle) * topRadius
          topPoints.push(new THREE.Vector3(x, height, z))
        }

        // 底部网格线
        const bottomGeometry = new THREE.BufferGeometry().setFromPoints(points)
        const bottomMaterial = new THREE.LineBasicMaterial({
          color: 0x00ffff,
          transparent: true,
          opacity: 0.4 + (level * 0.1)
        })
        const bottomLine = new THREE.Line(bottomGeometry, bottomMaterial)
        this.gridGroup.add(bottomLine)

        // 顶部网格线
        const topGeometry = new THREE.BufferGeometry().setFromPoints(topPoints)
        const topMaterial = new THREE.LineBasicMaterial({
          color: 0x00ffff,
          transparent: true,
          opacity: 0.5 + (level * 0.1)
        })
        const topLine = new THREE.Line(topGeometry, topMaterial)
        this.gridGroup.add(topLine)

        // 垂直连接线（宝塔边缘）
        for (let i = 0; i < sides; i++) {
          const angle = (i * 2 * Math.PI) / sides - Math.PI / 2
          const bottomX = Math.cos(angle) * radius
          const bottomZ = Math.sin(angle) * radius
          const topX = Math.cos(angle) * topRadius
          const topZ = Math.sin(angle) * topRadius

          const verticalPoints = [
            new THREE.Vector3(bottomX, 0, bottomZ),
            new THREE.Vector3(topX, height, topZ)
          ]

          const verticalGeometry = new THREE.BufferGeometry().setFromPoints(verticalPoints)
          const verticalMaterial = new THREE.LineBasicMaterial({
            color: 0x17e6c3,
            transparent: true,
            opacity: 0.3 + (level * 0.1)
          })
          const verticalLine = new THREE.Line(verticalGeometry, verticalMaterial)
          this.gridGroup.add(verticalLine)
        }
      }

      // 创建主轴线（从中心到边缘）
      for (let i = 0; i < sides; i++) {
        const angle = (i * 2 * Math.PI) / sides - Math.PI / 2
        const x = Math.cos(angle) * this.radarRadius
        const z = Math.sin(angle) * this.radarRadius

        const points = [
          new THREE.Vector3(0, 0, 0),
          new THREE.Vector3(x, 0, z),
          new THREE.Vector3(x * 0.25, this.towerHeight, z * 0.25) // 延伸到塔顶
        ]

        const geometry = new THREE.BufferGeometry().setFromPoints(points)
        const material = new THREE.LineBasicMaterial({
          color: 0x17e6c3,
          transparent: true,
          opacity: 0.8
        })
        const line = new THREE.Line(geometry, material)
        this.gridGroup.add(line)
      }

      this.scene.add(this.gridGroup)
    },

    createRadarData() {
      this.data.forEach((dataset, dataIndex) => {
        const bottomPoints = []
        const topPoints = []
        const sides = this.indicators.length

        // 计算底部和顶部数据点的位置（宝塔形状）
        for (let i = 0; i < sides; i++) {
          const angle = (i * 2 * Math.PI) / sides - Math.PI / 2
          const value = dataset.value[i] || 0
          const normalizedValue = value / this.maxValue

          // 底部点
          const bottomRadius = normalizedValue * this.radarRadius
          const bottomX = Math.cos(angle) * bottomRadius
          const bottomZ = Math.sin(angle) * bottomRadius
          const bottomY = dataIndex * 0.15
          bottomPoints.push(new THREE.Vector3(bottomX, bottomY, bottomZ))

          // 顶部点（收缩形成宝塔）
          const topRadius = bottomRadius * 0.3 // 顶部收缩到30%
          const topX = Math.cos(angle) * topRadius
          const topZ = Math.sin(angle) * topRadius
          const topY = bottomY + (normalizedValue * this.towerHeight)
          topPoints.push(new THREE.Vector3(topX, topY, topZ))
        }

        // 闭合多边形
        bottomPoints.push(bottomPoints[0].clone())
        topPoints.push(topPoints[0].clone())

        const color = new THREE.Color(this.colors[dataIndex % this.colors.length])

        // 创建底部线条
        const bottomLineGeometry = new THREE.BufferGeometry().setFromPoints(bottomPoints)
        const bottomLineMaterial = new THREE.LineBasicMaterial({
          color: color,
          transparent: true,
          opacity: 0.9,
          linewidth: 4
        })
        const bottomLine = new THREE.Line(bottomLineGeometry, bottomLineMaterial)
        bottomLine.name = `radarBottomLine${dataIndex}`

        // 创建顶部线条
        const topLineGeometry = new THREE.BufferGeometry().setFromPoints(topPoints)
        const topLineMaterial = new THREE.LineBasicMaterial({
          color: color,
          transparent: true,
          opacity: 0.9,
          linewidth: 4
        })
        const topLine = new THREE.Line(topLineGeometry, topLineMaterial)
        topLine.name = `radarTopLine${dataIndex}`

        // 创建侧面连接线和面
        for (let i = 0; i < sides; i++) {
          const nextI = (i + 1) % sides

          // 侧面连接线
          const sidePoints = [
            bottomPoints[i],
            topPoints[i],
            topPoints[nextI],
            bottomPoints[nextI],
            bottomPoints[i]
          ]

          const sideLineGeometry = new THREE.BufferGeometry().setFromPoints(sidePoints)
          const sideLineMaterial = new THREE.LineBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.6
          })
          const sideLine = new THREE.Line(sideLineGeometry, sideLineMaterial)
          sideLine.name = `radarSideLine${dataIndex}_${i}`
          this.radarGroup.add(sideLine)

          // 侧面填充
          const sideGeometry = new THREE.BufferGeometry()
          const vertices = new Float32Array([
            bottomPoints[i].x, bottomPoints[i].y, bottomPoints[i].z,
            topPoints[i].x, topPoints[i].y, topPoints[i].z,
            bottomPoints[nextI].x, bottomPoints[nextI].y, bottomPoints[nextI].z,

            topPoints[i].x, topPoints[i].y, topPoints[i].z,
            topPoints[nextI].x, topPoints[nextI].y, topPoints[nextI].z,
            bottomPoints[nextI].x, bottomPoints[nextI].y, bottomPoints[nextI].z
          ])
          sideGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3))
          sideGeometry.computeVertexNormals()

          const sideFillMaterial = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: this.opacity * 0.4,
            side: THREE.DoubleSide
          })
          const sideFill = new THREE.Mesh(sideGeometry, sideFillMaterial)
          sideFill.name = `radarSideFill${dataIndex}_${i}`
          this.radarGroup.add(sideFill)
        }

        // 创建底部填充面
        const bottomShape = new THREE.Shape()
        bottomShape.moveTo(bottomPoints[0].x, bottomPoints[0].z)
        for (let i = 1; i < bottomPoints.length - 1; i++) {
          bottomShape.lineTo(bottomPoints[i].x, bottomPoints[i].z)
        }

        const bottomFillGeometry = new THREE.ShapeGeometry(bottomShape)
        const bottomFillMaterial = new THREE.MeshBasicMaterial({
          color: color,
          transparent: true,
          opacity: this.opacity * 0.5,
          side: THREE.DoubleSide
        })
        const bottomFill = new THREE.Mesh(bottomFillGeometry, bottomFillMaterial)
        bottomFill.rotation.x = -Math.PI / 2
        bottomFill.position.y = dataIndex * 0.15
        bottomFill.name = `radarBottomFill${dataIndex}`

        // 创建顶部填充面
        const topShape = new THREE.Shape()
        topShape.moveTo(topPoints[0].x, topPoints[0].z)
        for (let i = 1; i < topPoints.length - 1; i++) {
          topShape.lineTo(topPoints[i].x, topPoints[i].z)
        }

        const topFillGeometry = new THREE.ShapeGeometry(topShape)
        const topFillMaterial = new THREE.MeshBasicMaterial({
          color: color,
          transparent: true,
          opacity: this.opacity * 0.6,
          side: THREE.DoubleSide
        })
        const topFill = new THREE.Mesh(topFillGeometry, topFillMaterial)
        topFill.rotation.x = -Math.PI / 2
        topFill.position.y = topPoints[0].y
        topFill.name = `radarTopFill${dataIndex}`

        // 创建数据点（底部和顶部）
        bottomPoints.slice(0, -1).forEach((point, pointIndex) => {
          const dotGeometry = new THREE.SphereGeometry(0.08, 12, 12)
          const dotMaterial = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 1
          })
          const dot = new THREE.Mesh(dotGeometry, dotMaterial)
          dot.position.copy(point)
          dot.name = `radarBottomDot${dataIndex}_${pointIndex}`
          this.radarGroup.add(dot)
        })

        topPoints.slice(0, -1).forEach((point, pointIndex) => {
          const dotGeometry = new THREE.SphereGeometry(0.06, 12, 12)
          const dotMaterial = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 1
          })
          const dot = new THREE.Mesh(dotGeometry, dotMaterial)
          dot.position.copy(point)
          dot.name = `radarTopDot${dataIndex}_${pointIndex}`
          this.radarGroup.add(dot)
        })

        this.radarGroup.add(bottomLine)
        this.radarGroup.add(topLine)
        this.radarGroup.add(bottomFill)
        this.radarGroup.add(topFill)
      })

      this.scene.add(this.radarGroup)
      this.animateRadar()
    },

    createLabels() {
      this.indicators.forEach((indicator, index) => {
        const angle = (index * 2 * Math.PI) / this.indicators.length - Math.PI / 2
        const x = Math.cos(angle) * (this.radarRadius + 0.8)
        const z = Math.sin(angle) * (this.radarRadius + 0.8)

        // 创建文字纹理
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        canvas.width = 128
        canvas.height = 32

        context.fillStyle = '#ffffff'
        context.font = 'bold 16px Arial'
        context.textAlign = 'center'
        context.shadowColor = '#00ffff'
        context.shadowBlur = 10
        context.fillText(indicator, 64, 20)

        const texture = new THREE.CanvasTexture(canvas)
        const material = new THREE.SpriteMaterial({
          map: texture,
          transparent: true,
          alphaTest: 0.1
        })
        const sprite = new THREE.Sprite(material)
        sprite.position.set(x, this.towerHeight * 0.6, z) // 提高标签位置
        sprite.scale.set(1.2, 0.3, 1)

        this.radarGroup.add(sprite)
      })
    },

    animateRadar() {
      // 初始动画
      this.radarGroup.children.forEach((child, index) => {
        if (child.name.startsWith('radarLine') || child.name.startsWith('radarFill')) {
          child.scale.set(0, 0, 0)
          gsap.to(child.scale, {
            x: 1,
            y: 1,
            z: 1,
            duration: 1.5,
            delay: index * 0.2,
            ease: "back.out(1.7)"
          })
        }
        if (child.name.startsWith('radarDot')) {
          child.scale.set(0, 0, 0)
          gsap.to(child.scale, {
            x: 1,
            y: 1,
            z: 1,
            duration: 0.8,
            delay: index * 0.1 + 1,
            ease: "elastic.out(1, 0.5)"
          })
        }
      })
      
      // 开始循环高亮动画
      setTimeout(() => {
        this.timer = setInterval(() => {
          this.highlightData()
        }, this.delay)
      }, 2500)
    },

    highlightData() {
      // 重置所有数据
      this.radarGroup.children.forEach(child => {
        if (child.name.startsWith('radarLine')) {
          gsap.to(child.material, { opacity: 0.8, duration: 0.5 })
        }
        if (child.name.startsWith('radarFill')) {
          gsap.to(child.material, { opacity: this.opacity * 0.3, duration: 0.5 })
        }
        if (child.name.startsWith('radarDot')) {
          gsap.to(child.scale, { x: 1, y: 1, z: 1, duration: 0.5 })
        }
      })
      
      // 高亮当前数据
      const currentLine = this.radarGroup.getObjectByName(`radarLine${this.activeIndex}`)
      const currentFill = this.radarGroup.getObjectByName(`radarFill${this.activeIndex}`)
      
      if (currentLine) {
        gsap.to(currentLine.material, { opacity: 1, duration: 0.5 })
      }
      
      if (currentFill) {
        gsap.to(currentFill.material, { opacity: this.opacity * 0.6, duration: 0.5 })
      }
      
      // 高亮对应的数据点
      for (let i = 0; i < this.indicators.length; i++) {
        const dot = this.radarGroup.getObjectByName(`radarDot${this.activeIndex}_${i}`)
        if (dot) {
          gsap.to(dot.scale, { x: 1.5, y: 1.5, z: 1.5, duration: 0.5 })
        }
      }
      
      this.activeIndex = (this.activeIndex + 1) % this.data.length
    },

    initCamera() {
      const rate = this.width / this.height
      this.camera = new THREE.PerspectiveCamera(45, rate, 0.1, 1000)
      this.camera.position.set(5, 4, 5)
      this.camera.lookAt(0, 0, 0)
    },

    initRenderer() {
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        premultipliedAlpha: false,
      })
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.setSize(this.width, this.height)
      this.renderer.setClearColor(0x000000, 0)
      this.$refs["radarDom"].appendChild(this.renderer.domElement)
    },

    initLight() {
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.8)
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5)
      directionalLight.position.set(5, 5, 5)
      
      this.scene.add(ambientLight)
      this.scene.add(directionalLight)
    },

    initControls() {
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      this.controls.enableZoom = true
      this.controls.enablePan = false
      this.controls.maxPolarAngle = Math.PI / 2.2
      this.controls.minDistance = 4
      this.controls.maxDistance = 12
      this.controls.autoRotate = true
      this.controls.autoRotateSpeed = 0.5
    },

    loop() {
      this.controls.update()
      this.renderer.render(this.scene, this.camera)
      requestAnimationFrame(() => this.loop())
    },

    resize() {
      this.width = this.$refs["radarDom"].offsetWidth
      this.height = this.$refs["radarDom"].offsetHeight
      this.camera.aspect = this.width / this.height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(this.width, this.height)
    },

    destroy() {
      clearInterval(this.timer)
      if (this.renderer) {
        emptyObject(this.radarGroup)
        emptyObject(this.gridGroup)
        this.renderer.dispose()
        this.renderer.forceContextLoss()
        this.controls.dispose()
        this.$refs["radarDom"].innerHTML = ""
        this.scene = null
        this.camera = null
        this.renderer = null
        this.controls = null
      }
    },
  },
}
</script>

<style>
.three-radar-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.three-radar {
  width: 100%;
  height: 100%;
}
</style>
