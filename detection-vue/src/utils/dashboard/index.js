export function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
export function throttle(func, delay) {
  let timer = null
  return function (...args) {
    if (!timer) {
      func(...args)
      timer = setTimeout(() => {
        timer = null
      }, delay)
    }
  }
}

// Three.js cleanup utility function
export function emptyObject(obj) {
  if (!obj) return
  
  if (obj.geometry) {
    obj.geometry.dispose()
  }
  
  if (obj.material) {
    if (Array.isArray(obj.material)) {
      obj.material.forEach(material => {
        if (material.map) material.map.dispose()
        if (material.dispose) material.dispose()
      })
    } else {
      if (obj.material.map) obj.material.map.dispose()
      if (obj.material.dispose) obj.material.dispose()
    }
  }
  
  if (obj.children) {
    obj.children.forEach(child => emptyObject(child))
  }
  
  if (obj.clear) {
    obj.clear()
  }
  
  if (obj.dispose) {
    obj.dispose()
  }
}
