@font-face {
  font-family: "D-DIN";
  src: url("~@/assets/font/D-DIN.otf") format("otf");
}
@font-face {
  font-family: "D-DIN-Bold";
  src: url("~@/assets/font/D-DIN-Bold.otf") format("otf");
}
@font-face {
  font-family: "D-DIN-Italic";
  src: url("~@/assets/font/D-DIN-Italic.otf") format("otf");
}
@font-face {
  font-family: "Alibaba PuHuiTi";
  src: url("~@/assets/font/AlibabaPuHuiTi.woff2") format("woff2");
}
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: "阿里妈妈数黑体 Bold";
  font-weight: 700;
  src: url("~@/assets/font/alimama/EUt83SjhF4Bt.woff2") format("woff2"),
    url("~@/assets/font/alimama/EUt83SjhF4Bt.woff") format("woff");
  font-display: swap;
}
